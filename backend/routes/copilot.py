from fastapi import APIRouter, HTTPException, status
from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field
import uuid

from firebase.init import get_firestore_client

router = APIRouter()

# Request/Response Models
class CopilotQuery(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)

class CopilotResponse(BaseModel):
    response: str
    data: Optional[Dict[str, Any]] = None
    suggestions: Optional[List[str]] = None
    query_id: str
    timestamp: datetime

class ChatMessage(BaseModel):
    id: str
    query: str
    response: str
    timestamp: datetime
    user_id: Optional[str] = None

class FeedbackRequest(BaseModel):
    query_id: str
    rating: int = Field(..., ge=1, le=5)
    feedback: Optional[str] = None

@router.post("/ask", response_model=CopilotResponse)
async def ask_copilot(query_data: CopilotQuery):
    """
    Main AI Copilot query processing endpoint
    """
    try:
        db = get_firestore_client()
        query_id = str(uuid.uuid4())
        
        # TODO: Replace with actual LLM integration
        # For now, provide mock responses based on query patterns
        response_text, structured_data = await process_query(query_data.query, db)
        
        # Save query to history
        chat_record = {
            "id": query_id,
            "query": query_data.query,
            "response": response_text,
            "timestamp": datetime.utcnow(),
            "context": query_data.context
        }
        
        # Store in Firestore (mock implementation)
        try:
            db.collection('copilot_history').document(query_id).set(chat_record)
        except Exception as e:
            print(f"Warning: Could not save chat history: {e}")
        
        return CopilotResponse(
            response=response_text,
            data=structured_data,
            suggestions=generate_follow_up_suggestions(query_data.query),
            query_id=query_id,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing query: {str(e)}"
        )

@router.get("/history", response_model=List[ChatMessage])
async def get_chat_history(limit: int = 50, offset: int = 0):
    """
    Retrieve chat history with pagination
    """
    try:
        db = get_firestore_client()
        
        # Get chat history from Firestore
        history_ref = db.collection('copilot_history')
        history_docs = history_ref.limit(limit).offset(offset).stream()
        
        history = []
        for doc in history_docs:
            data = doc.to_dict()
            if data:
                history.append(ChatMessage(
                    id=data.get('id', doc.id),
                    query=data.get('query', ''),
                    response=data.get('response', ''),
                    timestamp=data.get('timestamp', datetime.utcnow()),
                    user_id=data.get('user_id')
                ))
        
        return history
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving chat history: {str(e)}"
        )

@router.post("/feedback")
async def submit_feedback(feedback_data: FeedbackRequest):
    """
    Collect user feedback for query improvement
    """
    try:
        db = get_firestore_client()

        feedback_record = {
            "query_id": feedback_data.query_id,
            "rating": feedback_data.rating,
            "feedback": feedback_data.feedback,
            "timestamp": datetime.utcnow()
        }

        # Store feedback in Firestore
        db.collection('copilot_feedback').add(feedback_record)

        return {"message": "Feedback submitted successfully"}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error submitting feedback: {str(e)}"
        )

@router.get("/insights")
async def get_live_insights():
    """
    Get real-time insights and alerts for the insights feed
    """
    try:
        db = get_firestore_client()

        # Get recent portfolio changes and generate insights
        insights = []

        # Get MSMEs for analysis
        msmes_ref = db.collection('msmes')
        msmes_docs = msmes_ref.stream()

        high_risk_count = 0
        declining_count = 0
        total_msmes = 0

        for doc in msmes_docs:
            data = doc.to_dict()
            if data:
                total_msmes += 1
                if data.get('risk_band') == 'red':
                    high_risk_count += 1
                if data.get('score_trend') == 'declining':
                    declining_count += 1

        # Generate insights based on data
        if high_risk_count > 0:
            insights.append({
                "id": "high_risk_alert",
                "type": "alert",
                "severity": "high" if high_risk_count > total_msmes * 0.3 else "medium",
                "title": f"{high_risk_count} MSMEs in high risk category",
                "description": f"Requires immediate attention and review",
                "timestamp": datetime.utcnow().isoformat(),
                "actionable": True
            })

        if declining_count > 0:
            insights.append({
                "id": "declining_scores",
                "type": "trend",
                "severity": "medium",
                "title": f"{declining_count} MSMEs showing declining trends",
                "description": "Score deterioration detected",
                "timestamp": datetime.utcnow().isoformat(),
                "actionable": True
            })

        # Add compliance insights (mock data)
        insights.append({
            "id": "compliance_deadlines",
            "type": "compliance",
            "severity": "high",
            "title": "12 CRILC filings due soon",
            "description": "Deadlines within 7 days",
            "timestamp": datetime.utcnow().isoformat(),
            "actionable": True
        })

        return {"insights": insights}

    except Exception as e:
        # Return mock insights if database query fails
        return {
            "insights": [
                {
                    "id": "mock_insight",
                    "type": "alert",
                    "severity": "medium",
                    "title": "Portfolio monitoring active",
                    "description": "Real-time insights being processed",
                    "timestamp": datetime.utcnow().isoformat(),
                    "actionable": False
                }
            ]
        }

async def process_query(query: str, db) -> tuple[str, Optional[Dict[str, Any]]]:
    """
    Process natural language query and return response with structured data
    Enhanced with better pattern matching and context understanding
    """
    query_lower = query.lower()

    # Enhanced query processing with better pattern matching
    if any(keyword in query_lower for keyword in ["high risk", "alert", "priority", "urgent", "critical"]):
        return await handle_risk_query(query, db)
    elif any(keyword in query_lower for keyword in ["score drop", "declining", "deteriorat", "worsen", "fall"]):
        return await handle_score_analysis_query(query, db)
    elif any(keyword in query_lower for keyword in ["compliance", "deadline", "filing", "rbi", "crilc", "overdue"]):
        return await handle_compliance_query(query, db)
    elif any(keyword in query_lower for keyword in ["branch", "location", "geographic", "region", "area"]):
        return await handle_geographic_query(query, db)
    elif any(keyword in query_lower for keyword in ["sector", "manufacturing", "retail", "industry", "business type"]):
        return await handle_sector_query(query, db)
    elif any(keyword in query_lower for keyword in ["portfolio", "summary", "overview", "dashboard"]):
        return await handle_portfolio_overview_query(query, db)
    elif any(keyword in query_lower for keyword in ["trend", "pattern", "analysis", "insight"]):
        return await handle_trend_analysis_query(query, db)
    else:
        return await handle_general_query(query, db)

async def handle_risk_query(query: str, db) -> tuple[str, Dict[str, Any]]:
    """Handle risk-related queries"""
    try:
        # Get actual data from Firestore
        msmes_ref = db.collection('msmes')
        msmes_docs = msmes_ref.stream()

        high_risk_msmes = []
        total_msmes = 0
        risk_distribution = {"green": 0, "yellow": 0, "red": 0}

        for doc in msmes_docs:
            data = doc.to_dict()
            if data:
                total_msmes += 1
                risk_band = data.get('risk_band', 'red')
                risk_distribution[risk_band] = risk_distribution.get(risk_band, 0) + 1

                if risk_band == 'red':
                    high_risk_msmes.append({
                        "id": doc.id,
                        "name": data.get('name', 'Unknown'),
                        "score": data.get('current_score', 0),
                        "business_type": data.get('business_type', 'Unknown'),
                        "location": data.get('location', 'Unknown')
                    })

        high_risk_count = len(high_risk_msmes)

        response = f"""Based on your portfolio analysis, I found {high_risk_count} MSMEs with high-priority risk alerts:

**High Risk MSMEs:**"""

        for i, msme in enumerate(high_risk_msmes[:5]):  # Show top 5
            response += f"""
• {msme['name']} (Score: {msme['score']}) - {msme['business_type']} in {msme['location']}"""

        if high_risk_count > 5:
            response += f"""
... and {high_risk_count - 5} more MSMEs

**Portfolio Risk Distribution:**
• High Risk (Red): {risk_distribution['red']} MSMEs ({risk_distribution['red']/total_msmes*100:.1f}%)
• Medium Risk (Yellow): {risk_distribution['yellow']} MSMEs ({risk_distribution['yellow']/total_msmes*100:.1f}%)
• Low Risk (Green): {risk_distribution['green']} MSMEs ({risk_distribution['green']/total_msmes*100:.1f}%)

**Recommended Actions:**
• Schedule field visits for top {min(3, high_risk_count)} high-risk MSMEs
• Review credit limits and exposure
• Initiate enhanced monitoring protocols

Would you like me to provide detailed analysis for any specific MSME or risk category?"""

        structured_data = {
            "type": "risk_analysis",
            "summary": {
                "total_high_risk": high_risk_count,
                "total_msmes": total_msmes,
                "risk_distribution": risk_distribution
            },
            "msmes": high_risk_msmes[:10]  # Return top 10 for detailed view
        }

        return response, structured_data

    except Exception as e:
        # Fallback to mock data if Firestore query fails
        response = f"""I encountered an issue accessing the portfolio data. Here's what I can tell you based on cached information:

**Portfolio Risk Overview:**
• Monitoring {total_msmes if 'total_msmes' in locals() else 'multiple'} MSMEs across your portfolio
• Risk assessment and alerts are being processed
• Recommend manual review of high-risk accounts

Please try your query again, or contact support if the issue persists."""

        return response, {"type": "error", "message": str(e)}

async def handle_score_analysis_query(query: str, db) -> tuple[str, Dict[str, Any]]:
    """Handle score analysis queries"""
    try:
        # Get MSMEs with score trends
        msmes_ref = db.collection('msmes')
        msmes_docs = msmes_ref.stream()

        declining_msmes = []
        total_analyzed = 0
        score_changes = []

        for doc in msmes_docs:
            data = doc.to_dict()
            if data:
                total_analyzed += 1
                score_trend = data.get('score_trend', 'stable')
                current_score = data.get('current_score', 0)

                if score_trend == 'declining' or current_score < 40:
                    # Simulate score drop calculation
                    previous_score = current_score + 15 + (total_analyzed % 10)  # Mock previous score
                    score_drop = previous_score - current_score

                    if score_drop > 15:
                        declining_msmes.append({
                            "id": doc.id,
                            "name": data.get('name', 'Unknown'),
                            "current_score": current_score,
                            "previous_score": previous_score,
                            "score_drop": score_drop,
                            "business_type": data.get('business_type', 'Unknown'),
                            "location": data.get('location', 'Unknown')
                        })
                        score_changes.append(score_drop)

        avg_drop = sum(score_changes) / len(score_changes) if score_changes else 0

        response = f"""Score deterioration analysis for the last 30 days:

**MSMEs with Significant Score Drops (>15 points):**"""

        for msme in declining_msmes[:5]:  # Show top 5
            response += f"""
• {msme['name']}: {msme['previous_score']:.0f} → {msme['current_score']:.0f} (-{msme['score_drop']:.0f} points)"""

        if len(declining_msmes) > 5:
            response += f"""
... and {len(declining_msmes) - 5} more MSMEs

**Analysis Summary:**
• Total MSMEs with significant drops: {len(declining_msmes)}
• Average score decline: {avg_drop:.1f} points
• Most affected sectors: {', '.join(set([m['business_type'] for m in declining_msmes[:3]]))}

**Primary Causes (estimated):**
• GST turnover decline (60% of cases)
• Banking irregularities (25% of cases)
• Delayed payments (15% of cases)

**Recommended Actions:**
• Schedule field visits for top {min(3, len(declining_msmes))} MSMEs
• Request updated financial statements
• Initiate enhanced monitoring protocols"""

        structured_data = {
            "type": "score_analysis",
            "period": "30_days",
            "msmes_affected": len(declining_msmes),
            "avg_score_drop": avg_drop,
            "declining_msmes": declining_msmes[:10],
            "top_causes": ["gst_decline", "banking_issues", "payment_delays"]
        }

        return response, structured_data

    except Exception as e:
        # Fallback response
        response = """Score deterioration analysis is currently being processed.

Based on recent trends, I recommend:
• Regular monitoring of MSMEs with declining payment patterns
• Review of GST compliance across the portfolio
• Enhanced due diligence for high-risk accounts

Please try again in a moment for detailed analysis."""

        return response, {"type": "error", "message": str(e)}

async def handle_compliance_query(query: str, db) -> tuple[str, Dict[str, Any]]:
    """Handle compliance-related queries"""
    response = """Compliance Status Overview:

**Overdue Items:**
• 12 CRILC filings due within 7 days
• 8 EWS checklist updates pending
• 5 SMA classification reviews overdue

**Upcoming Deadlines (Next 30 days):**
• 25 CRILC quarterly filings
• 15 NPA review meetings
• 10 regulatory audit preparations

**Priority Actions:**
1. Complete overdue CRILC filings immediately
2. Schedule EWS checklist reviews
3. Prepare documentation for upcoming audits"""
    
    structured_data = {
        "type": "compliance_dashboard",
        "overdue_count": 25,
        "upcoming_count": 50,
        "critical_deadlines": 12
    }
    
    return response, structured_data

async def handle_geographic_query(query: str, db) -> tuple[str, Dict[str, Any]]:
    """Handle geographic/branch analysis queries"""
    response = """Branch Risk Distribution Analysis:

**High Risk Concentration:**
• Nashik Branch: 45% high risk (exceeds 40% threshold)
• Pune Branch: 38% high risk (approaching threshold)
• Mumbai Branch: 25% high risk (within limits)

**Recommendations:**
• Immediate portfolio review for Nashik branch
• Enhanced monitoring for Pune branch
• Consider risk diversification strategies

**Geographic Risk Factors:**
• Regional economic slowdown in Nashik
• Sector concentration in manufacturing
• Limited diversification in business types"""
    
    structured_data = {
        "type": "geographic_analysis",
        "branches": [
            {"name": "Nashik", "high_risk_pct": 45, "status": "critical"},
            {"name": "Pune", "high_risk_pct": 38, "status": "warning"},
            {"name": "Mumbai", "high_risk_pct": 25, "status": "normal"}
        ]
    }
    
    return response, structured_data

async def handle_sector_query(query: str, db) -> tuple[str, Dict[str, Any]]:
    """Handle sector analysis queries"""
    response = """Manufacturing Sector Performance Analysis:

**Current Quarter Trends:**
• 25% decline in average GST turnover
• 15% increase in payment delays
• 30% of MSMEs showing stress signals

**Risk Indicators:**
• Raw material cost inflation
• Supply chain disruptions
• Reduced order volumes

**Sector Recommendations:**
• Enhanced monitoring for manufacturing MSMEs
• Consider sector-specific support programs
• Review exposure limits for manufacturing"""
    
    structured_data = {
        "type": "sector_analysis",
        "sector": "manufacturing",
        "performance_change": -25,
        "msmes_at_risk": 30
    }
    
    return response, structured_data

async def handle_portfolio_overview_query(query: str, db) -> tuple[str, Dict[str, Any]]:
    """Handle portfolio overview queries"""
    try:
        # Get portfolio statistics
        msmes_ref = db.collection('msmes')
        msmes_docs = msmes_ref.stream()

        total_msmes = 0
        risk_distribution = {"green": 0, "yellow": 0, "red": 0}
        business_types = {}
        total_score = 0

        for doc in msmes_docs:
            data = doc.to_dict()
            if data:
                total_msmes += 1
                risk_band = data.get('risk_band', 'red')
                risk_distribution[risk_band] = risk_distribution.get(risk_band, 0) + 1

                business_type = data.get('business_type', 'Unknown')
                business_types[business_type] = business_types.get(business_type, 0) + 1

                total_score += data.get('current_score', 0)

        avg_score = total_score / total_msmes if total_msmes > 0 else 0

        response = f"""**Portfolio Overview Summary**

**Portfolio Size:** {total_msmes} MSMEs
**Average Score:** {avg_score:.1f}

**Risk Distribution:**
• Low Risk (Green): {risk_distribution['green']} MSMEs ({risk_distribution['green']/total_msmes*100:.1f}%)
• Medium Risk (Yellow): {risk_distribution['yellow']} MSMEs ({risk_distribution['yellow']/total_msmes*100:.1f}%)
• High Risk (Red): {risk_distribution['red']} MSMEs ({risk_distribution['red']/total_msmes*100:.1f}%)

**Business Type Distribution:**"""

        for business_type, count in sorted(business_types.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_msmes) * 100
            response += f"""
• {business_type.title()}: {count} MSMEs ({percentage:.1f}%)"""

        response += f"""

**Key Insights:**
• Portfolio health score: {85 - (risk_distribution['red'] * 2):.1f}/100
• Risk concentration: {'High' if risk_distribution['red']/total_msmes > 0.3 else 'Moderate' if risk_distribution['red']/total_msmes > 0.15 else 'Low'}
• Diversification index: {min(100, len(business_types) * 20):.0f}/100

Would you like me to dive deeper into any specific area?"""

        structured_data = {
            "type": "portfolio_overview",
            "total_msmes": total_msmes,
            "avg_score": avg_score,
            "risk_distribution": risk_distribution,
            "business_types": business_types,
            "health_score": 85 - (risk_distribution['red'] * 2)
        }

        return response, structured_data

    except Exception as e:
        return await handle_general_query(query, db)

async def handle_trend_analysis_query(query: str, db) -> tuple[str, Dict[str, Any]]:
    """Handle trend analysis queries"""
    try:
        # Get MSMEs for trend analysis
        msmes_ref = db.collection('msmes')
        msmes_docs = msmes_ref.stream()

        trends = {"improving": 0, "stable": 0, "declining": 0}
        sector_trends = {}

        for doc in msmes_docs:
            data = doc.to_dict()
            if data:
                trend = data.get('score_trend', 'stable')
                trends[trend] = trends.get(trend, 0) + 1

                business_type = data.get('business_type', 'Unknown')
                if business_type not in sector_trends:
                    sector_trends[business_type] = {"improving": 0, "stable": 0, "declining": 0}
                sector_trends[business_type][trend] += 1

        total_msmes = sum(trends.values())

        response = f"""**Portfolio Trend Analysis**

**Overall Trends:**
• Improving: {trends['improving']} MSMEs ({trends['improving']/total_msmes*100:.1f}%)
• Stable: {trends['stable']} MSMEs ({trends['stable']/total_msmes*100:.1f}%)
• Declining: {trends['declining']} MSMEs ({trends['declining']/total_msmes*100:.1f}%)

**Sector-wise Trends:**"""

        for sector, sector_trend in sector_trends.items():
            total_sector = sum(sector_trend.values())
            declining_pct = (sector_trend['declining'] / total_sector) * 100 if total_sector > 0 else 0
            response += f"""
• {sector.title()}: {declining_pct:.1f}% declining"""

        response += f"""

**Key Observations:**
• Portfolio momentum: {'Positive' if trends['improving'] > trends['declining'] else 'Negative' if trends['declining'] > trends['improving'] else 'Neutral'}
• Sectors needing attention: {', '.join([s for s, t in sector_trends.items() if t['declining'] > t['improving']])}
• Overall trend direction: {'Improving' if trends['improving'] > trends['declining'] else 'Declining' if trends['declining'] > trends['improving'] else 'Stable'}

Would you like detailed analysis for any specific sector or trend?"""

        structured_data = {
            "type": "trend_analysis",
            "overall_trends": trends,
            "sector_trends": sector_trends,
            "momentum": "positive" if trends['improving'] > trends['declining'] else "negative" if trends['declining'] > trends['improving'] else "neutral"
        }

        return response, structured_data

    except Exception as e:
        return await handle_general_query(query, db)

async def handle_general_query(query: str, db) -> tuple[str, Dict[str, Any]]:
    """Handle general queries"""
    response = f"""I understand you're asking about: "{query}"

I can help you with:
• **Portfolio Analysis** - Overview, trends, and health metrics
• **Risk Management** - High-risk alerts and mitigation strategies
• **Score Analysis** - Deterioration patterns and improvement opportunities
• **Compliance Tracking** - Deadlines, overdue tasks, and regulatory requirements
• **Geographic Analysis** - Branch-wise risk distribution and concentration
• **Sector Insights** - Industry trends and performance patterns

**Try asking:**
• "Show me my portfolio overview"
• "Which MSMEs need immediate attention?"
• "What are the trending patterns this quarter?"
• "Analyze compliance status across branches"

How can I assist you today?"""

    return response, None

def generate_follow_up_suggestions(query: str) -> List[str]:
    """Generate contextual follow-up suggestions"""
    query_lower = query.lower()
    
    if "risk" in query_lower:
        return [
            "Show me the detailed risk breakdown for these MSMEs",
            "What actions should I take for high-risk MSMEs?",
            "How has the risk distribution changed over time?"
        ]
    elif "score" in query_lower:
        return [
            "What caused these score drops?",
            "Show me the score recovery trends",
            "Which parameters contributed most to the decline?"
        ]
    elif "compliance" in query_lower:
        return [
            "Show me the compliance checklist for these items",
            "What are the penalties for missing these deadlines?",
            "How can I automate compliance tracking?"
        ]
    else:
        return [
            "Show me today's portfolio summary",
            "What are the top priority actions?",
            "Analyze trends for the last quarter"
        ]
