import { MSME, Analytics, ScoreDetails, Signal, SignalInput, Nudge, NudgeRequest, EnhancedScoreDetails } from '@/types';

// AI Copilot types
export interface CopilotQuery {
  query: string;
  context?: Record<string, any>;
}

export interface CopilotResponse {
  response: string;
  data?: Record<string, any>;
  suggestions?: string[];
  query_id: string;
  timestamp: string;
}

export interface ChatMessage {
  id: string;
  query: string;
  response: string;
  timestamp: string;
  user_id?: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

class ApiError extends Error {
  constructor(message: string, public status: number) {
    super(message);
    this.name = 'ApiError';
  }
}

async function fetchApi<T>(endpoint: string, options?: {
  method?: string;
  body?: any;
}): Promise<T> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: options?.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      ...(options?.body && { body: JSON.stringify(options.body) }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new ApiError(`HTTP error! status: ${response.status} - ${errorText}`, response.status);
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`, 0);
  }
}

export const api = {
  // Dashboard Analytics
  async getAnalytics(): Promise<Analytics> {
    return fetchApi<Analytics>('/dashboard/analytics');
  },

  // Portfolio Management
  async getPortfolio(): Promise<MSME[]> {
    return fetchApi<MSME[]>('/dashboard/portfolio');
  },

  // MSME Management
  async getMSME(id: string): Promise<MSME> {
    return fetchApi<MSME>(`/msme/${id}`);
  },

  async getMSMEScore(id: string): Promise<ScoreDetails> {
    return fetchApi<ScoreDetails>(`/msme/${id}/score`);
  },

  async getMSMEEnhancedScore(id: string): Promise<EnhancedScoreDetails> {
    return fetchApi<EnhancedScoreDetails>(`/msme/${id}/enhanced-score`);
  },

  // Signal Management
  async getMSMESignals(id: string, limit?: number): Promise<Signal[]> {
    const endpoint = `/msme/${id}/signals${limit ? `?limit=${limit}` : ''}`;
    return fetchApi<Signal[]>(endpoint);
  },

  async addSignalToMSME(msmeId: string, signalInput: SignalInput): Promise<{
    signal_id: string;
    msme_id: string;
    signal_data: Signal;
    score_update: any;
  }> {
    return fetchApi(`/msme/${msmeId}/signals`, {
      method: 'POST',
      body: signalInput,
    });
  },

  // Nudge Management
  async getMSMENudges(id: string, limit?: number): Promise<Nudge[]> {
    const endpoint = `/msme/${id}/nudges${limit ? `?limit=${limit}` : ''}`;
    return fetchApi<Nudge[]>(endpoint);
  },

  async sendNudgeToMSME(msmeId: string, nudgeRequest: NudgeRequest): Promise<Nudge> {
    return fetchApi<Nudge>(`/msme/${msmeId}/nudges`, {
      method: 'POST',
      body: nudgeRequest,
    });
  },

  async sendBulkNudges(msmeIds: string[], nudgeRequest: NudgeRequest): Promise<{
    total_requested: number;
    successful: number;
    failed: number;
    nudge_ids: string[];
    errors: Array<{ msme_id: string; error: string }>;
  }> {
    return fetchApi('/nudges/bulk', {
      method: 'POST',
      body: {
        msme_ids: msmeIds,
        trigger_type: nudgeRequest.trigger_type,
        message: nudgeRequest.message,
        medium: nudgeRequest.medium,
        metadata: nudgeRequest.metadata,
      },
    });
  },

  // Health check
  async healthCheck(): Promise<{ status: string; service: string }> {
    return fetchApi<{ status: string; service: string }>('/health');
  },

  // AI Copilot
  async askCopilot(query: CopilotQuery): Promise<CopilotResponse> {
    return fetchApi<CopilotResponse>('/api/copilot/ask', {
      method: 'POST',
      body: query,
    });
  },

  async getCopilotHistory(limit?: number, offset?: number): Promise<ChatMessage[]> {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());

    const endpoint = `/api/copilot/history${params.toString() ? `?${params.toString()}` : ''}`;
    return fetchApi<ChatMessage[]>(endpoint);
  },

  async submitCopilotFeedback(queryId: string, rating: number, feedback?: string): Promise<{ message: string }> {
    return fetchApi<{ message: string }>('/api/copilot/feedback', {
      method: 'POST',
      body: {
        query_id: queryId,
        rating,
        feedback,
      },
    });
  },

  async getCopilotInsights(): Promise<{ insights: any[] }> {
    return fetchApi<{ insights: any[] }>('/api/copilot/insights');
  },

  async getComplianceHealth(): Promise<any> {
    return fetchApi<any>('/api/copilot/compliance-health');
  }
};
