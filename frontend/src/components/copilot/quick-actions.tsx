'use client';

import { Button } from '@/components/ui/button';
import {
  Alert<PERSON>riangle,
  TrendingDown,
  FileCheck,
  BarChart3,
  Building2,
  Clock
} from 'lucide-react';

interface QuickActionsProps {
  onQuerySelect: (query: string) => void;
}

const quickActions = [
  {
    title: 'High Risk Alerts',
    query: 'Show me all high priority alerts that need immediate attention',
    icon: AlertTriangle,
    count: '12'
  },
  {
    title: 'Score Drops',
    query: 'Which MSMEs have had significant score drops in the last 30 days?',
    icon: TrendingDown,
    count: '8'
  },
  {
    title: 'Compliance Tasks',
    query: 'Show me all pending compliance tasks and upcoming deadlines',
    icon: FileCheck,
    count: '5'
  },
  {
    title: 'Branch Analysis',
    query: 'Analyze risk distribution across all branch locations',
    icon: BarChart3,
    count: null
  },
  {
    title: 'Sector Trends',
    query: 'Show sector-wise stress analysis and emerging risk trends',
    icon: Building2,
    count: null
  },
  {
    title: 'Deadlines',
    query: 'What are the critical compliance deadlines coming up this week?',
    icon: Clock,
    count: '3'
  }
];

export function QuickActions({ onQuerySelect }: QuickActionsProps) {
  return (
    <div className="h-full">
      <div className="p-6 border-b border-border/40">
        <h3 className="font-medium text-foreground">Quick Actions</h3>
        <p className="text-sm text-muted-foreground mt-1">
          Common queries and analysis
        </p>
      </div>

      <div className="p-6 space-y-2">
        {quickActions.map((action, index) => (
          <Button
            key={index}
            variant="ghost"
            className="w-full justify-start h-auto p-3 hover:bg-muted/50"
            onClick={() => onQuerySelect(action.query)}
          >
            <div className="flex items-center gap-3 w-full">
              <action.icon className="h-4 w-4 text-muted-foreground" />
              <div className="flex-1 text-left">
                <div className="text-sm font-medium text-foreground">
                  {action.title}
                </div>
              </div>
              {action.count && (
                <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                  {action.count}
                </div>
              )}
            </div>
          </Button>
        ))}
      </div>
    </div>
  );
}
