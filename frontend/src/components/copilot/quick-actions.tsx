'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  TrendingDown, 
  FileCheck, 
  BarChart3,
  Building2,
  Clock,
  Target,
  Shield
} from 'lucide-react';

interface QuickActionsProps {
  onQuerySelect: (query: string) => void;
}

const quickActions = [
  {
    id: 'high-priority-alerts',
    title: 'High Priority Alerts',
    description: 'Unresolved alerts by severity',
    icon: AlertTriangle,
    query: 'Show me all high priority alerts that need immediate attention',
    badge: { text: '12 Active', variant: 'destructive' as const },
    color: 'red'
  },
  {
    id: 'score-deterioration',
    title: 'Score Deterioration',
    description: 'MSMEs with declining trends',
    icon: TrendingDown,
    query: 'Which MSMEs have had significant score drops in the last 30 days?',
    badge: { text: '8 MSMEs', variant: 'secondary' as const },
    color: 'orange'
  },
  {
    id: 'compliance-dashboard',
    title: 'Compliance Dashboard',
    description: 'Pending RBI requirements',
    icon: FileCheck,
    query: 'Show me all pending compliance tasks and upcoming deadlines',
    badge: { text: '5 Overdue', variant: 'destructive' as const },
    color: 'blue'
  },
  {
    id: 'branch-risk-summary',
    title: 'Branch Risk Summary',
    description: 'Risk distribution by location',
    icon: BarChart3,
    query: 'Analyze risk distribution across all branch locations',
    badge: { text: 'Updated', variant: 'default' as const },
    color: 'green'
  },
  {
    id: 'sector-stress-analysis',
    title: 'Sector Stress Analysis',
    description: 'Industry-wise risk patterns',
    icon: Building2,
    query: 'Show sector-wise stress analysis and emerging risk trends',
    badge: { text: 'Manufacturing ↓', variant: 'secondary' as const },
    color: 'purple'
  },
  {
    id: 'upcoming-deadlines',
    title: 'Upcoming Deadlines',
    description: 'Critical dates this week',
    icon: Clock,
    query: 'What are the critical compliance deadlines coming up this week?',
    badge: { text: '3 This Week', variant: 'secondary' as const },
    color: 'yellow'
  }
];

export function QuickActions({ onQuerySelect }: QuickActionsProps) {
  return (
    <div className="space-y-4 p-4">
      {quickActions.map((action) => (
        <Card 
          key={action.id}
          className="cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-[1.02]"
          onClick={() => onQuerySelect(action.query)}
        >
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-emerald-100 dark:bg-emerald-900">
                  <action.icon className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                </div>
                <div>
                  <CardTitle className="text-sm font-medium">
                    {action.title}
                  </CardTitle>
                </div>
              </div>
              <Badge variant={action.badge.variant} className="text-xs">
                {action.badge.text}
              </Badge>
            </div>
            <CardDescription className="text-xs">
              {action.description}
            </CardDescription>
          </CardHeader>
        </Card>
      ))}
      
      {/* Custom Filters Section */}
      <div className="pt-4 border-t">
        <div className="text-sm font-medium mb-3 text-muted-foreground">
          Custom Filters
        </div>
        
        <div className="space-y-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full justify-start gap-2 text-xs"
            onClick={() => onQuerySelect('Filter MSMEs by risk band and business type')}
          >
            <Target className="h-3 w-3" />
            Advanced Filters
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full justify-start gap-2 text-xs"
            onClick={() => onQuerySelect('Show portfolio health metrics and recommendations')}
          >
            <Shield className="h-3 w-3" />
            Portfolio Health
          </Button>
        </div>
      </div>
    </div>
  );
}
