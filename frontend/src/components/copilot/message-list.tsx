'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Bo<PERSON>, User, ExternalLink, TrendingDown, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: any;
}

interface MessageListProps {
  messages: Message[];
}

export function MessageList({ messages }: MessageListProps) {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderStructuredData = (data: any) => {
    if (!data || typeof data !== 'object') return null;

    switch (data.type) {
      case 'risk_analysis':
        return renderRiskAnalysis(data);
      case 'score_analysis':
        return renderScoreAnalysis(data);
      case 'compliance_dashboard':
        return renderComplianceData(data);
      case 'geographic_analysis':
        return renderGeographicData(data);
      default:
        return (
          <div className="text-xs opacity-75">
            Additional data available
          </div>
        );
    }
  };

  const renderRiskAnalysis = (data: any) => (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <AlertTriangle className="h-4 w-4 text-red-500" />
        <span className="text-sm font-medium">Risk Analysis Summary</span>
      </div>
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div>
          <span className="text-muted-foreground">High Risk MSMEs:</span>
          <span className="ml-1 font-medium">{data.summary?.total_high_risk || 0}</span>
        </div>
        <div>
          <span className="text-muted-foreground">Total Portfolio:</span>
          <span className="ml-1 font-medium">{data.summary?.total_msmes || 0}</span>
        </div>
      </div>
      {data.msmes && data.msmes.length > 0 && (
        <div className="mt-2">
          <Button variant="outline" size="sm" className="text-xs h-6">
            <ExternalLink className="h-3 w-3 mr-1" />
            View Details ({data.msmes.length} MSMEs)
          </Button>
        </div>
      )}
    </div>
  );

  const renderScoreAnalysis = (data: any) => (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <TrendingDown className="h-4 w-4 text-orange-500" />
        <span className="text-sm font-medium">Score Analysis</span>
      </div>
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div>
          <span className="text-muted-foreground">MSMEs Affected:</span>
          <span className="ml-1 font-medium">{data.msmes_affected || 0}</span>
        </div>
        <div>
          <span className="text-muted-foreground">Avg Drop:</span>
          <span className="ml-1 font-medium">{data.avg_score_drop?.toFixed(1) || 0} pts</span>
        </div>
      </div>
      {data.declining_msmes && data.declining_msmes.length > 0 && (
        <div className="mt-2">
          <Button variant="outline" size="sm" className="text-xs h-6">
            <ExternalLink className="h-3 w-3 mr-1" />
            View Declining MSMEs ({data.declining_msmes.length})
          </Button>
        </div>
      )}
    </div>
  );

  const renderComplianceData = (data: any) => (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">Compliance Status</span>
      </div>
      <div className="flex gap-2">
        <Badge variant="destructive" className="text-xs">
          {data.overdue_count || 0} Overdue
        </Badge>
        <Badge variant="secondary" className="text-xs">
          {data.upcoming_count || 0} Upcoming
        </Badge>
      </div>
    </div>
  );

  const renderGeographicData = (data: any) => (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">Geographic Analysis</span>
      </div>
      {data.branches && (
        <div className="space-y-1">
          {data.branches.slice(0, 3).map((branch: any, index: number) => (
            <div key={index} className="flex items-center justify-between text-xs">
              <span>{branch.name}</span>
              <Badge
                variant={branch.status === 'critical' ? 'destructive' : branch.status === 'warning' ? 'secondary' : 'default'}
                className="text-xs"
              >
                {branch.high_risk_pct}% High Risk
              </Badge>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-4">
      {messages.map((message) => (
        <div
          key={message.id}
          className={cn(
            'flex gap-3',
            message.type === 'user' ? 'justify-end' : 'justify-start'
          )}
        >
          {message.type === 'assistant' && (
            <Avatar className="h-8 w-8 mt-1">
              <AvatarFallback className="bg-emerald-100 dark:bg-emerald-900">
                <Bot className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
              </AvatarFallback>
            </Avatar>
          )}
          
          <div
            className={cn(
              'max-w-[80%] space-y-1',
              message.type === 'user' ? 'items-end' : 'items-start'
            )}
          >
            <Card
              className={cn(
                'shadow-sm',
                message.type === 'user'
                  ? 'bg-emerald-600 text-white border-emerald-600'
                  : 'bg-muted'
              )}
            >
              <CardContent className="p-3">
                <div className="text-sm whitespace-pre-wrap">
                  {message.content}
                </div>
                
                {/* Render structured data if available */}
                {message.data && (
                  <div className="mt-3 pt-3 border-t border-emerald-500/20">
                    {renderStructuredData(message.data)}
                  </div>
                )}
              </CardContent>
            </Card>
            
            <div
              className={cn(
                'text-xs text-muted-foreground px-1',
                message.type === 'user' ? 'text-right' : 'text-left'
              )}
            >
              {formatTime(message.timestamp)}
            </div>
          </div>
          
          {message.type === 'user' && (
            <Avatar className="h-8 w-8 mt-1">
              <AvatarFallback className="bg-slate-100 dark:bg-slate-800">
                <User className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
          )}
        </div>
      ))}
    </div>
  );
}
