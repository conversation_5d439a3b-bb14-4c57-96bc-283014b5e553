'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  AlertTriangle, 
  TrendingDown, 
  FileCheck, 
  MapPin, 
  Building2,
  Calendar
} from 'lucide-react';

interface QuerySuggestionsProps {
  onSuggestionClick: (suggestion: string) => void;
}

const suggestions = [
  {
    icon: AlertTriangle,
    text: "Show MSMEs with high risk alerts today",
    category: "Risk Management"
  },
  {
    icon: TrendingDown,
    text: "Which MSMEs had score drops > 15 points in last 30 days?",
    category: "Score Analysis"
  },
  {
    icon: FileCheck,
    text: "List overdue compliance tasks for Gujarat region",
    category: "Compliance"
  },
  {
    icon: MapPin,
    text: "Show risk distribution by branch location",
    category: "Geographic Analysis"
  },
  {
    icon: Building2,
    text: "Manufacturing sector performance this quarter",
    category: "Sector Analysis"
  },
  {
    icon: Calendar,
    text: "Upcoming CRILC filing deadlines this month",
    category: "Deadlines"
  }
];

export function QuerySuggestions({ onSuggestionClick }: QuerySuggestionsProps) {
  return (
    <div className="space-y-3">
      <div className="text-sm font-medium text-muted-foreground">
        Try asking about:
      </div>
      
      <div className="grid grid-cols-1 gap-2">
        {suggestions.map((suggestion, index) => (
          <Card 
            key={index}
            className="cursor-pointer hover:bg-accent/50 transition-colors"
            onClick={() => onSuggestionClick(suggestion.text)}
          >
            <CardContent className="p-3">
              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-emerald-100 dark:bg-emerald-900 flex-shrink-0">
                  <suggestion.icon className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-foreground mb-1">
                    {suggestion.text}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {suggestion.category}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <div className="text-xs text-muted-foreground text-center pt-2">
        Or type your own question about portfolio management, compliance, or risk analysis
      </div>
    </div>
  );
}
