'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  MapPin,
  Building2,
  FileCheck,
  X,
  ExternalLink
} from 'lucide-react';

interface Insight {
  id: string;
  type: 'alert' | 'trend' | 'compliance' | 'geographic';
  severity: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  timestamp: Date;
  actionable: boolean;
  dismissed?: boolean;
}

interface InsightsFeedProps {}

export function InsightsFeed({}: InsightsFeedProps) {
  const [insights, setInsights] = useState<Insight[]>([
    {
      id: '1',
      type: 'alert',
      severity: 'high',
      title: 'Score Band Migration Alert',
      description: '5 MSMEs moved from Medium to High risk today',
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      actionable: true
    },
    {
      id: '2',
      type: 'trend',
      severity: 'medium',
      title: 'Manufacturing Sector Decline',
      description: 'Manufacturing sector showing 25% GST decline this quarter',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      actionable: true
    },
    {
      id: '3',
      type: 'compliance',
      severity: 'high',
      title: 'CRILC Filing Deadline',
      description: '12 CRILC filings due within 7 days',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      actionable: true
    },
    {
      id: '4',
      type: 'geographic',
      severity: 'medium',
      title: 'Branch Risk Concentration',
      description: 'Nashik branch exceeding 40% High risk threshold',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      actionable: true
    },
    {
      id: '5',
      type: 'trend',
      severity: 'low',
      title: 'Digital Payment Growth',
      description: 'UPI adoption increased 15% across retail MSMEs',
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
      actionable: false
    }
  ]);

  const getInsightIcon = (type: Insight['type']) => {
    switch (type) {
      case 'alert':
        return AlertTriangle;
      case 'trend':
        return TrendingUp;
      case 'compliance':
        return FileCheck;
      case 'geographic':
        return MapPin;
      default:
        return Building2;
    }
  };

  const getSeverityColor = (severity: Insight['severity']) => {
    switch (severity) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  const dismissInsight = (id: string) => {
    setInsights(prev => prev.map(insight => 
      insight.id === id ? { ...insight, dismissed: true } : insight
    ));
  };

  const activeInsights = insights.filter(insight => !insight.dismissed);

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium">
            Real-time Insights
          </div>
          <Badge variant="outline" className="text-xs">
            {activeInsights.length} Active
          </Badge>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-3">
          {activeInsights.map((insight) => {
            const Icon = getInsightIcon(insight.type);
            
            return (
              <Card key={insight.id} className="relative">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex items-center justify-center w-6 h-6 rounded-md bg-emerald-100 dark:bg-emerald-900">
                        <Icon className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
                      </div>
                      <Badge variant={getSeverityColor(insight.severity)} className="text-xs">
                        {insight.severity.toUpperCase()}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => dismissInsight(insight.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  <CardTitle className="text-sm font-medium">
                    {insight.title}
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <p className="text-xs text-muted-foreground mb-2">
                    {insight.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {formatTimeAgo(insight.timestamp)}
                    </div>
                    
                    {insight.actionable && (
                      <Button variant="ghost" size="sm" className="h-6 text-xs gap-1">
                        <ExternalLink className="h-3 w-3" />
                        View
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
          
          {activeInsights.length === 0 && (
            <div className="text-center py-8">
              <div className="text-sm text-muted-foreground">
                No active insights
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                New insights will appear here as they're detected
              </div>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
