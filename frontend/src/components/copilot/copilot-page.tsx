'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChatInterface } from './chat-interface';
import { QuickActions } from './quick-actions';
import { InsightsFeed } from './insights-feed';
import { ArrowLeft, Bot, Zap, TrendingUp } from 'lucide-react';
import Link from 'next/link';

export function CopilotPage() {
  const [activeQuery, setActiveQuery] = useState<string>('');

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-emerald-100 dark:bg-emerald-900">
              <Bot className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">AI Copilot</h1>
              <p className="text-sm text-muted-foreground">
                Intelligent assistant for MSME portfolio management
              </p>
            </div>
          </div>
        </div>
        
        <Link href="/">
          <Button variant="outline" size="sm" className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Portfolio
          </Button>
        </Link>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
          {/* Quick Actions - Left Sidebar */}
          <div className="lg:col-span-1">
            <Card className="h-full">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Zap className="h-5 w-5 text-emerald-600" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <QuickActions onQuerySelect={setActiveQuery} />
              </CardContent>
            </Card>
          </div>

          {/* Chat Interface - Center */}
          <div className="lg:col-span-2">
            <Card className="h-full">
              <CardContent className="p-0 h-full">
                <ChatInterface activeQuery={activeQuery} />
              </CardContent>
            </Card>
          </div>

          {/* Insights Feed - Right Sidebar */}
          <div className="lg:col-span-1">
            <Card className="h-full">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <TrendingUp className="h-5 w-5 text-emerald-600" />
                  Live Insights
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <InsightsFeed />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
